{"info": {"name": "<PERSON> Logging Tests", "description": "Test collection for Winston error logging in packaging microservice", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Validation Error Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invalid\": \"data\",\n  \"missing\": \"required fields\"\n}"}, "url": {"raw": "http://localhost:3000/api/v1/packages", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "packages"]}, "description": "Tests Zod validation errors with deeply nested error logging"}}, {"name": "2. Invalid ObjectId Error", "request": {"method": "GET", "url": {"raw": "http://localhost:3000/api/v1/packages/invalid-id", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "packages", "invalid-id"]}, "description": "Tests MongoDB ObjectId validation error"}}, {"name": "3. 404 Not Found Error", "request": {"method": "GET", "url": {"raw": "http://localhost:3000/api/v1/packages/507f1f77bcf86cd799439011", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "packages", "507f1f77bcf86cd799439011"]}, "description": "Tests 404 error for non-existent package"}}, {"name": "4. Invalid Route Error", "request": {"method": "GET", "url": {"raw": "http://localhost:3000/api/v1/nonexistent-route", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "nonexistent-route"]}, "description": "Tests 404 error for invalid endpoint"}}, {"name": "5. Complex Validation Error", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": 123,\n  \"shortDescription\": null,\n  \"description\": \"\",\n  \"variations\": \"not-an-array\",\n  \"images\": {},\n  \"metadata\": {\n    \"deeply\": {\n      \"nested\": {\n        \"invalid\": {\n          \"data\": [1, 2, 3, \"mixed\", {\"types\": true}]\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "http://localhost:3000/api/v1/packages", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "packages"]}, "description": "Tests complex validation errors with deeply nested data"}}, {"name": "6. Rate Limiting Test (Run Multiple Times)", "request": {"method": "GET", "url": {"raw": "http://localhost:3000/api/v1/packages", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "packages"]}, "description": "Send this request rapidly 16+ times to trigger rate limiting"}}]}