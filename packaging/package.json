{"name": "packaging-microservice", "version": "1.0.0", "description": "Packaging microservice for Coconut backend services", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "rimraf dist && tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["packaging", "microservice", "nodejs", "typescript", "express"], "author": "Coconut Team", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.10", "@types/node": "^20.10.4", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "rimraf": "^5.0.5", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}