import PackageModel, { PackageDocument } from "../models/Package";
import axios from "axios";

export class PackageService {
  static async addPackage(packageData: any): Promise<PackageDocument> {
    try {
      const newPackage = new PackageModel(packageData);
      return await newPackage.save();
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        throw new Error(`External API error: ${error.message}`);
      }
      throw error;
    }
  }

  static async getAllPackages(): Promise<PackageDocument[]> {
    try {
      return await PackageModel.find().sort({ createdAt: -1 });
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        throw new Error(`External API error: ${error.message}`);
      }
      throw error;
    }
  }

  static async getPackageById(id: string): Promise<PackageDocument | null> {
    try {
      return await PackageModel.findById(id);
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        throw new Error(`External API error: ${error.message}`);
      }
      throw error;
    }
  }

  static async updatePackage(id: string, updateData: any): Promise<PackageDocument | null> {
    try {
      return await PackageModel.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        throw new Error(`External API error: ${error.message}`);
      }
      throw error;
    }
  }

  static async deletePackage(id: string): Promise<PackageDocument | null> {
    try {
      return await PackageModel.findByIdAndDelete(id);
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        throw new Error(`External API error: ${error.message}`);
      }
      throw error;
    }
  }
}