import mongoose, { Document, Schema } from "mongoose";

export interface Variation {
  title: string;
  optionType: "dropdown" | "checkbox" | "radio";
  options: string[];
}

export interface Quantity {
  size: string;
  price: number;
}

export interface PackageDocument extends Document {
  title: string;
  shortDescription: string;
  description: string;
  variations: Variation[];
  images: string[];
  quantity: Quantity[];
  createdAt: Date;
  updatedAt: Date;
}

const VariationSchema = new Schema<Variation>({
  title: {
    type: String,
    required: true,
  },
  optionType: {
    type: String,
    enum: ["dropdown", "checkbox", "radio"],
    required: true,
  },
  options: [{
    type: String,
    required: true,
  }],
});

const QuantitySchema = new Schema<Quantity>({
  size: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
});

const PackageSchema = new Schema<PackageDocument>({
  title: {
    type: String,
    required: true,
  },
  shortDescription: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  variations: [VariationSchema],
  images: [{
    type: String,
    required: true,
  }],
  quantity: [QuantitySchema],
}, {
  timestamps: true,
});

export default mongoose.model<PackageDocument>("Package", PackageSchema);