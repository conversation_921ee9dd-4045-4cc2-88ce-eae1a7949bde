import { Request, Response, NextFunction } from "express";
import { logDeepError } from "./logger";

// Type for async controller functions
type AsyncController = (req: Request, res: Response, next: NextFunction) => Promise<any>;

/**
 * Wrapper for async controllers to handle promise rejections
 * This ensures that any unhandled promise rejections in controllers
 * are properly caught and passed to the error handler middleware
 */
export const asyncHandler = (fn: AsyncController) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      // Log the async error with full context
      logDeepError(error, `Async controller error in ${req.method} ${req.originalUrl}`, req, {
        controller: fn.name || 'anonymous',
        route: req.route?.path,
        params: req.params,
        query: req.query,
        body: req.method !== 'GET' ? req.body : undefined,
      });
      
      // Pass error to error handler middleware
      next(error);
    });
  };
};

/**
 * Wrapper for async middleware functions
 */
export const asyncMiddleware = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      logDeepError(error, `Async middleware error in ${req.method} ${req.originalUrl}`, req, {
        middleware: fn.name || 'anonymous',
      });
      next(error);
    });
  };
};

/**
 * Higher-order function to create error-safe async functions
 * Useful for service layer functions
 */
export const safeAsync = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: string
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      logDeepError(error, context || `Error in ${fn.name || 'anonymous function'}`, undefined, {
        functionName: fn.name,
        arguments: args,
      });
      throw error; // Re-throw to maintain error flow
    }
  };
};
