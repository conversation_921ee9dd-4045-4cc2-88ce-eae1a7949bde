import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import { env } from "./envValidator";
import path from "path";
import { Request } from "express";

// Custom format for deeply nested error logging
const errorStackFormat = winston.format((info) => {
  if (info.error && info.error instanceof Error) {
    info.stack = info.error.stack;
    info.errorName = info.error.name;
    info.errorMessage = info.error.message;
    
    // Handle nested errors and circular references
    info.errorDetails = JSON.stringify(info.error, Object.getOwnPropertyNames(info.error), 2);
    
    // Extract additional error properties
    const errorProps: any = {};
    Object.getOwnPropertyNames(info.error).forEach(key => {
      if (key !== 'name' && key !== 'message' && key !== 'stack') {
        try {
          errorProps[key] = (info.error as any)[key];
        } catch (e) {
          errorProps[key] = '[Circular Reference or Non-serializable]';
        }
      }
    });
    
    if (Object.keys(errorProps).length > 0) {
      info.errorProperties = errorProps;
    }
  }
  return info;
});

// Custom format for request context
const requestContextFormat = winston.format((info) => {
  if (info.req) {
    const req = info.req as Request;
    info.requestContext = {
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl,
      ip: req.ip || req.connection?.remoteAddress,
      userAgent: req.get('User-Agent'),
      headers: req.headers,
      body: req.method !== 'GET' ? req.body : undefined,
      params: req.params,
      query: req.query,
      timestamp: new Date().toISOString(),
    };
    delete info.req; // Remove the original req object to avoid circular references
  }
  return info;
});

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Define log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  errorStackFormat(),
  requestContextFormat(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, stack, errorName, requestContext }) => {
    let logMessage = `${timestamp} [${level}]: ${message}`;

    if (errorName) {
      logMessage += `\n  Error: ${errorName}`;
    }

    if (requestContext && typeof requestContext === 'object') {
      const ctx = requestContext as any;
      if (ctx.method && ctx.url) {
        logMessage += `\n  Request: ${ctx.method} ${ctx.url}`;
      }
      if (ctx.ip) {
        logMessage += `\n  IP: ${ctx.ip}`;
      }
    }

    if (stack) {
      logMessage += `\n  Stack: ${stack}`;
    }

    return logMessage;
  })
);

// Configure transports
const transports: winston.transport[] = [];

// Console transport for development
if (env.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: env.LOG_LEVEL,
    })
  );
} else {
  // Simplified console for production
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      level: 'error',
    })
  );
}

// File transports with rotation
transports.push(
  // Error logs
  new DailyRotateFile({
    filename: path.join(logsDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    format: logFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
  }),
  
  // Combined logs
  new DailyRotateFile({
    filename: path.join(logsDir, 'combined-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: logFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
    level: env.LOG_LEVEL,
  }),
  
  // HTTP access logs
  new DailyRotateFile({
    filename: path.join(logsDir, 'access-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'http',
    format: logFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
  })
);

// Create the logger
const logger = winston.createLogger({
  level: env.LOG_LEVEL,
  format: logFormat,
  transports,
  exitOnError: false,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: env.LOG_FILE_MAX_SIZE,
      maxFiles: env.LOG_FILE_MAX_FILES,
      zippedArchive: true,
    })
  ],
  rejectionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: env.LOG_FILE_MAX_SIZE,
      maxFiles: env.LOG_FILE_MAX_FILES,
      zippedArchive: true,
    })
  ]
});

// Helper functions for structured logging
export const logError = (error: Error, context?: any, req?: Request) => {
  logger.error({
    message: error.message,
    error,
    context,
    req,
  });
};

export const logWarning = (message: string, context?: any, req?: Request) => {
  logger.warn({
    message,
    context,
    req,
  });
};

export const logInfo = (message: string, context?: any, req?: Request) => {
  logger.info({
    message,
    context,
    req,
  });
};

export const logHttp = (message: string, context?: any, req?: Request) => {
  logger.http({
    message,
    context,
    req,
  });
};

export const logDebug = (message: string, context?: any, req?: Request) => {
  logger.debug({
    message,
    context,
    req,
  });
};

// Function to log deeply nested errors with full context
export const logDeepError = (error: any, context: string, req?: Request, additionalData?: any) => {
  const errorInfo: any = {
    message: `${context}: ${error?.message || 'Unknown error'}`,
    context,
    req,
    additionalData,
  };

  // Handle different error types
  if (error instanceof Error) {
    errorInfo.error = error;
  } else if (typeof error === 'object' && error !== null) {
    // Handle non-Error objects that might contain nested error information
    errorInfo.errorObject = JSON.stringify(error, Object.getOwnPropertyNames(error), 2);
    errorInfo.errorType = 'Non-Error Object';
  } else {
    errorInfo.errorValue = error;
    errorInfo.errorType = typeof error;
  }

  logger.error(errorInfo);
};

export default logger;
