import { logDeepError, logError, logWarning, logInfo } from "../utils/logger";
import { ZodError } from "zod";

// Test function to demonstrate deep error logging capabilities
export const testErrorLogging = () => {
  console.log("🧪 Testing Winston Error Logging with Deeply Nested Errors...\n");

  // Test 1: Simple Error
  console.log("1. Testing simple error logging:");
  try {
    throw new Error("This is a simple test error");
  } catch (error) {
    logError(error as Error, { testCase: "Simple Error Test" });
  }

  // Test 2: Error with custom properties
  console.log("\n2. Testing error with custom properties:");
  try {
    const customError = new Error("Custom error with properties") as any;
    customError.statusCode = 500;
    customError.code = "CUSTOM_ERROR";
    customError.userId = "user123";
    customError.metadata = {
      operation: "test",
      timestamp: new Date(),
      nested: {
        level1: {
          level2: {
            deepValue: "This is deeply nested",
            array: [1, 2, 3, { nested: "object" }]
          }
        }
      }
    };
    throw customError;
  } catch (error) {
    logDeepError(error, "Custom Error Test", undefined, {
      additionalContext: "Testing custom error properties",
      testId: "test-002"
    });
  }

  // Test 3: Nested Error Chain
  console.log("\n3. Testing nested error chain:");
  try {
    const originalError = new Error("Original database error");
    (originalError as any).code = "ECONNREFUSED";
    (originalError as any).errno = -61;
    
    const middlewareError = new Error("Middleware processing failed");
    (middlewareError as any).cause = originalError;
    (middlewareError as any).layer = "middleware";
    
    const controllerError = new Error("Controller action failed");
    (controllerError as any).cause = middlewareError;
    (controllerError as any).action = "createPackage";
    (controllerError as any).userId = "user456";
    
    throw controllerError;
  } catch (error) {
    logDeepError(error, "Nested Error Chain Test", undefined, {
      errorChain: "controller -> middleware -> database",
      testId: "test-003"
    });
  }

  // Test 4: Zod Validation Error
  console.log("\n4. Testing Zod validation error:");
  try {
    const zodError = new ZodError([
      {
        code: "invalid_type",
        expected: "string",
        received: "number",
        path: ["user", "name"],
        message: "Expected string, received number"
      },
      {
        code: "too_small",
        minimum: 8,
        type: "string",
        inclusive: true,
        path: ["user", "password"],
        message: "String must contain at least 8 character(s)"
      }
    ]);
    throw zodError;
  } catch (error) {
    logDeepError(error, "Zod Validation Error Test", undefined, {
      validationContext: "User registration form",
      testId: "test-004"
    });
  }

  // Test 5: Circular Reference Error
  console.log("\n5. Testing circular reference error:");
  try {
    const obj1: any = { name: "Object 1" };
    const obj2: any = { name: "Object 2" };
    obj1.ref = obj2;
    obj2.ref = obj1; // Circular reference
    
    const circularError = new Error("Error with circular references") as any;
    circularError.circularData = obj1;
    circularError.metadata = {
      timestamp: new Date(),
      circular: obj1
    };
    
    throw circularError;
  } catch (error) {
    logDeepError(error, "Circular Reference Error Test", undefined, {
      note: "This tests handling of circular references in error objects",
      testId: "test-005"
    });
  }

  // Test 6: MongoDB-like Error
  console.log("\n6. Testing MongoDB-like error:");
  try {
    const mongoError = new Error("E11000 duplicate key error collection") as any;
    mongoError.name = "MongoServerError";
    mongoError.code = 11000;
    mongoError.codeName = "DuplicateKey";
    mongoError.keyPattern = { email: 1 };
    mongoError.keyValue = { email: "<EMAIL>" };
    mongoError.index = 0;
    mongoError.errmsg = "E11000 duplicate key error collection: test.users index: email_1 dup key: { email: \"<EMAIL>\" }";
    
    throw mongoError;
  } catch (error) {
    logDeepError(error, "MongoDB Duplicate Key Error Test", undefined, {
      collection: "users",
      operation: "insertOne",
      testId: "test-006"
    });
  }

  // Test 7: Async Error Chain
  console.log("\n7. Testing async error chain:");
  setTimeout(async () => {
    try {
      // Simulate async operation chain
      const asyncError1 = new Error("Async operation 1 failed");
      (asyncError1 as any).operation = "fetchUserData";
      (asyncError1 as any).timeout = 5000;
      
      const asyncError2 = new Error("Async operation 2 failed");
      (asyncError2 as any).cause = asyncError1;
      (asyncError2 as any).operation = "processUserData";
      
      const asyncError3 = new Error("Final async operation failed");
      (asyncError3 as any).cause = asyncError2;
      (asyncError3 as any).operation = "saveProcessedData";
      (asyncError3 as any).stack = `${asyncError3.stack}\nCaused by: ${asyncError2.stack}\nCaused by: ${asyncError1.stack}`;
      
      throw asyncError3;
    } catch (error) {
      logDeepError(error, "Async Error Chain Test", undefined, {
        asyncChain: "saveProcessedData -> processUserData -> fetchUserData",
        testId: "test-007"
      });
    }
  }, 100);

  // Test 8: Performance and Memory Usage
  console.log("\n8. Testing performance with large error objects:");
  try {
    const largeError = new Error("Error with large data") as any;
    largeError.largeArray = new Array(1000).fill(0).map((_, i) => ({
      id: i,
      data: `Large data item ${i}`,
      nested: {
        level1: { level2: { level3: `Deep data ${i}` } }
      }
    }));
    largeError.timestamp = new Date();
    largeError.memoryUsage = process.memoryUsage();
    
    throw largeError;
  } catch (error) {
    const startTime = Date.now();
    logDeepError(error, "Large Error Object Test", undefined, {
      note: "Testing performance with large error objects",
      testId: "test-008"
    });
    const endTime = Date.now();
    console.log(`   Logging took ${endTime - startTime}ms`);
  }

  console.log("\n✅ Error logging tests completed! Check the logs directory for detailed output.");
  console.log("📁 Log files location: ./logs/");
  console.log("🔍 Check error-*.log for error-level logs");
  console.log("🔍 Check combined-*.log for all logs");
};

// Test different log levels
export const testLogLevels = () => {
  console.log("\n🎯 Testing different log levels:");
  
  logInfo("This is an info message", { level: "info", testId: "level-001" });
  logWarning("This is a warning message", { level: "warning", testId: "level-002" });
  logError(new Error("This is an error message"), { level: "error", testId: "level-003" });
  
  console.log("✅ Log level tests completed!");
};

// Run tests if this file is executed directly
if (require.main === module) {
  testErrorLogging();
  testLogLevels();
}
