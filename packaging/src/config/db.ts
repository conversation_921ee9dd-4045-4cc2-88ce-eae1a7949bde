import mongoose from "mongoose";
import { env } from "../utils/envValidator";
import { logInfo, logError, logWarning } from "../utils/logger";

export const connectDB = async (): Promise<void> => {
  try {
    // Set mongoose options for better error handling
    mongoose.set('strictQuery', false);

    // Connect to MongoDB
    const conn = await mongoose.connect(env.MONGODB_URI, {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    });

    logInfo(`MongoDB Connected successfully`, {
      host: conn.connection.host,
      port: conn.connection.port,
      database: conn.connection.name,
      readyState: conn.connection.readyState,
    });

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logError(error, { context: 'MongoDB connection error' });
    });

    mongoose.connection.on('disconnected', () => {
      logWarning('MongoDB disconnected', { context: 'Database connection lost' });
    });

    mongoose.connection.on('reconnected', () => {
      logInfo('MongoDB reconnected', { context: 'Database connection restored' });
    });

    // Handle process termination
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logInfo('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (error) {
        logError(error as Error, { context: 'Error closing MongoDB connection' });
        process.exit(1);
      }
    });

  } catch (error) {
    logError(error as Error, {
      context: 'Failed to connect to MongoDB',
      mongoUri: env.MONGODB_URI.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
    });
    throw error; // Re-throw to let the caller handle it
  }
};
