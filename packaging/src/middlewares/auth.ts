import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { env } from "../utils/envValidator";

interface AuthRequest extends Request {
  user?: any;
}

export const protect = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;

    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        status: 401,
        message: "Not authorized to access this route",
      });
      return;
    }

    try {
      const decoded = jwt.verify(token, env.JWT_SECRET) as any;
      req.user = decoded;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        status: 401,
        message: "Not authorized to access this route",
      });
      return;
    }
  } catch (error) {
    next(error);
  }
};

export const adminProtect = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;

    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        status: 401,
        message: "Not authorized to access this route",
      });
      return;
    }

    try {
      const decoded = jwt.verify(token, env.ADMIN_JWT_SECRET) as any;
      req.user = decoded;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        status: 401,
        message: "Not authorized to access this route",
      });
      return;
    }
  } catch (error) {
    next(error);
  }
};