import dotenv from "dotenv";
dotenv.config();
import app from "./app";
import { env } from "./utils/envValidator";
import { logInfo, logError } from "./utils/logger";

const PORT = env.PORT || 3000;

const server = app.listen(PORT, () => {
  logInfo(`🚀 Server started successfully`, {
    port: PORT,
    environment: env.NODE_ENV,
    nodeVersion: process.version,
    platform: process.platform,
    pid: process.pid,
    timestamp: new Date().toISOString(),
  });

  logInfo(`📚 API Documentation available`, {
    swaggerUrl: `http://localhost:${PORT}/api-docs`,
    healthCheck: `http://localhost:${PORT}/health`,
  });
});

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  logInfo(`🛑 ${signal} received. Starting graceful shutdown...`);

  server.close((error) => {
    if (error) {
      logError(error, { context: 'Error during server shutdown' });
      process.exit(1);
    }

    logInfo('✅ Server closed successfully');
    process.exit(0);
  });

  // Force close after 10 seconds
  setTimeout(() => {
    logError(new Error('Forced shutdown after timeout'), {
      context: 'Graceful shutdown timeout'
    });
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle server errors
server.on('error', (error: any) => {
  if (error.code === 'EADDRINUSE') {
    logError(error, {
      context: `Port ${PORT} is already in use`,
      port: PORT
    });
  } else {
    logError(error, { context: 'Server error' });
  }
  process.exit(1);
});
