import { Request, Response, NextFunction } from "express";
import { PackageService } from "../services/Packageservice";
import { PackageDocument } from "../models/Package";
import { asyncHandler } from "../utils/asyncHandler";
import { logInfo, logWarning } from "../utils/logger";

export class PackageController {
  static add = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    logInfo(`Creating new package`, {
      packageData: req.body,
      userId: (req as any).user?.id
    }, req);

    const pkg: PackageDocument = await PackageService.addPackage(req.body);

    logInfo(`Package created successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(201).json({
      success: true,
      message: "Package created successfully",
      data: pkg,
    });
  });

  static update = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Updating package`, {
      packageId,
      updateData: req.body,
      userId: (req as any).user?.id
    }, req);

    const pkg = await PackageService.updatePackage(packageId, req.body);

    if (!pkg) {
      logWarning(`Package not found for update`, { packageId }, req);
      return res.status(404).json({
        success: false,
        message: "Package not found",
      });
    }

    logInfo(`Package updated successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package updated successfully",
      data: pkg,
    });
  });

  static getAll = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    logInfo(`Retrieving all packages`, {
      userId: (req as any).user?.id
    }, req);

    const packages: PackageDocument[] = await PackageService.getAllPackages();

    logInfo(`Retrieved ${packages.length} packages`, {
      count: packages.length
    }, req);

    res.status(200).json({
      success: true,
      message: "Packages retrieved successfully",
      data: packages,
      count: packages.length,
    });
  });

  static getById = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Retrieving package by ID`, { packageId }, req);

    const pkg = await PackageService.getPackageById(packageId);

    if (!pkg) {
      logWarning(`Package not found`, { packageId }, req);
      return res.status(404).json({
        success: false,
        message: "Package not found",
      });
    }

    logInfo(`Package retrieved successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package retrieved successfully",
      data: pkg,
    });
  });

  static delete = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Deleting package`, {
      packageId,
      userId: (req as any).user?.id
    }, req);

    const pkg = await PackageService.deletePackage(packageId);

    if (!pkg) {
      logWarning(`Package not found for deletion`, { packageId }, req);
      return res.status(404).json({
        success: false,
        message: "Package not found",
      });
    }

    logInfo(`Package deleted successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package deleted successfully",
      data: { deletedPackage: pkg },
    });
  });
}