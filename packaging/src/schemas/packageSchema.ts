import { z } from "zod";

const variationSchema = z.object({
  title: z.string().min(1, "Variation title is required"),
  optionType: z.enum(["dropdown", "checkbox", "radio"]),
  options: z.array(z.string().min(1, "Option cannot be empty")).min(1, "At least one option is required"),
});

const quantitySchema = z.object({
  size: z.string().min(1, "Size is required"),
  price: z.number().positive("Price must be positive"),
});

export const createPackageSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  shortDescription: z.string().min(1, "Short description is required").max(500, "Short description too long"),
  description: z.string().min(1, "Description is required"),
  variations: z.array(variationSchema).min(1, "At least one variation is required"),
  images: z.array(z.string().url("Invalid image URL")).min(1, "At least one image is required"),
  quantity: z.array(quantitySchema).min(1, "At least one quantity option is required"),
});

export const updatePackageSchema = createPackageSchema.partial();

export const packageIdSchema = z.object({
  id: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid package ID format"),
});
