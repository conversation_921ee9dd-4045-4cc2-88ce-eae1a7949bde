import express from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import { connectDB } from "./config/db";
import { errorHandler } from "./middlewares/errorHandler";
import { httpLogger, slowRequestLogger } from "./middlewares/httpLogger";
import { setupSwagger } from "./config/swaggerConfig";
import packageRoutes from "./routes/packageRoutes";
import logger, { logInfo, logError } from "./utils/logger";

const app = express();

// Connect to MongoDB with error handling
connectDB().catch((error) => {
  logError(error, { context: 'Database connection failed' });
  process.exit(1);
});

// Security middleware
app.use(helmet());
app.use(cors());

// Winston-based HTTP logging
app.use(httpLogger);
app.use(slowRequestLogger(2000)); // Log requests taking more than 2 seconds

// Rate limiting with logging
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    status: 429,
    message: "Too many requests from this IP, please try again later.",
  },
  handler: (req, res) => {
    logInfo(`Rate limit reached for IP: ${req.ip}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
    });

    res.status(429).json({
      success: false,
      status: 429,
      message: "Too many requests from this IP, please try again later.",
    });
  },
});

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter);

app.get("/", (req, res) => {
  logInfo("Health check endpoint accessed", { ip: req.ip });
  res.json({
    success: true,
    message: "Welcome to Coconut Packaging API",
    timestamp: new Date().toISOString(),
    version: "1.0.0"
  });
});

// Health check endpoint
app.get("/health", (req, res) => {
  logInfo("Health check endpoint accessed", { ip: req.ip });
  res.json({
    success: true,
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  });
});

// Routes
app.use("/api/v1/packages", packageRoutes);

// Setup Swagger API Docs
setupSwagger(app);

// Error handler must be last middleware
app.use(errorHandler);

// Handle 404 errors
app.use("*", (req, res) => {
  logInfo(`404 - Route not found: ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.originalUrl} not found`,
  });
});

// Global error handlers for uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  logError(error, { context: 'Uncaught Exception' });
  logger.error('Uncaught Exception occurred. Shutting down gracefully...');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(new Error(`Unhandled Rejection: ${reason}`), {
    context: 'Unhandled Promise Rejection',
    promise: promise.toString()
  });
  logger.error('Unhandled Promise Rejection occurred. Shutting down gracefully...');
  process.exit(1);
});

export default app;