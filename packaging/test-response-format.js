const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

async function testResponseFormats() {
  console.log('🧪 Testing Response Format Consistency...\n');

  const tests = [
    {
      name: 'Validation Error (POST /packages with invalid data)',
      request: () => axios.post(`${BASE_URL}/packages`, { invalid: 'data' }),
      expectedStatus: 400,
      expectedFields: ['success', 'message', 'errors']
    },
    {
      name: '404 Error (GET non-existent package)',
      request: () => axios.get(`${BASE_URL}/packages/507f1f77bcf86cd799439011`),
      expectedStatus: 404,
      expectedFields: ['success', 'message']
    },
    {
      name: 'Invalid ObjectId Error (GET with invalid ID)',
      request: () => axios.get(`${BASE_URL}/packages/invalid-id`),
      expectedStatus: 400,
      expectedFields: ['success', 'message', 'errors']
    },
    {
      name: '404 Route Not Found',
      request: () => axios.get(`${BASE_URL}/nonexistent-route`),
      expectedStatus: 404,
      expectedFields: ['success', 'message']
    }
  ];

  let allTestsPassed = true;

  for (const test of tests) {
    try {
      await test.request();
      console.log(`❌ ${test.name}: Expected error but got success`);
      allTestsPassed = false;
    } catch (error) {
      if (error.response) {
        const { status, data } = error.response;
        
        // Check status code
        if (status !== test.expectedStatus) {
          console.log(`❌ ${test.name}: Expected status ${test.expectedStatus}, got ${status}`);
          allTestsPassed = false;
          continue;
        }

        // Check response format
        const hasRedundantStatus = data.hasOwnProperty('status');
        const hasRequiredFields = test.expectedFields.every(field => data.hasOwnProperty(field));
        const hasSuccessFalse = data.success === false;

        if (hasRedundantStatus) {
          console.log(`❌ ${test.name}: Response contains redundant 'status' field`);
          console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
          allTestsPassed = false;
        } else if (!hasRequiredFields) {
          console.log(`❌ ${test.name}: Missing required fields. Expected: ${test.expectedFields.join(', ')}`);
          console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
          allTestsPassed = false;
        } else if (!hasSuccessFalse) {
          console.log(`❌ ${test.name}: Expected success: false, got success: ${data.success}`);
          allTestsPassed = false;
        } else {
          console.log(`✅ ${test.name}: Correct format`);
          console.log(`   Status: ${status}, Fields: ${Object.keys(data).join(', ')}`);
        }
      } else {
        console.log(`❌ ${test.name}: Network error - ${error.message}`);
        allTestsPassed = false;
      }
    }
    console.log('');
  }

  if (allTestsPassed) {
    console.log('🎉 All response format tests passed!');
    console.log('✅ Error responses follow consistent project structure:');
    console.log('   - No redundant "status" field');
    console.log('   - HTTP status codes are set correctly');
    console.log('   - All responses have "success: false" for errors');
    console.log('   - Required fields (success, message, errors?) are present');
  } else {
    console.log('❌ Some response format tests failed!');
    process.exit(1);
  }
}

// Run the tests
testResponseFormats().catch(error => {
  console.error('Test execution failed:', error.message);
  process.exit(1);
});
