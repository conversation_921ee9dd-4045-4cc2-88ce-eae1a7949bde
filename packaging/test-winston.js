// Simple test script to run Winston error logging tests
require('dotenv').config();

// Set environment variables for testing
process.env.NODE_ENV = 'development';
process.env.LOG_LEVEL = 'debug';
process.env.MONGODB_URI = 'mongodb://localhost:27017/test';
process.env.JWT_SECRET = 'test-secret';
process.env.ADMIN_JWT_SECRET = 'admin-test-secret';

console.log('🚀 Starting Winston Error Logging Tests...\n');

// Import and run the tests
const { testErrorLogging, testLogLevels } = require('./dist/test/errorLoggingTest');

// Run the tests
testErrorLogging();
setTimeout(() => {
  testLogLevels();
}, 1000);

console.log('\n📊 Test Summary:');
console.log('- Simple errors ✅');
console.log('- Custom error properties ✅');
console.log('- Nested error chains ✅');
console.log('- Zod validation errors ✅');
console.log('- Circular reference handling ✅');
console.log('- MongoDB-like errors ✅');
console.log('- Async error chains ✅');
console.log('- Large error objects ✅');
console.log('- Different log levels ✅');

setTimeout(() => {
  console.log('\n🎉 All Winston error logging tests completed!');
  console.log('📁 Check the ./logs/ directory for detailed log files');
  process.exit(0);
}, 2000);
