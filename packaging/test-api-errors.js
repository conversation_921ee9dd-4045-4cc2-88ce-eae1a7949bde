// Test script to demonstrate Winston error logging with API calls
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

console.log('🧪 Testing API Error Logging with Winston...\n');

async function testAPIErrors() {
  console.log('1. Testing validation error (invalid package data):');
  try {
    await axios.post(`${BASE_URL}/packages`, {
      // Missing required fields to trigger validation error
      invalidField: 'test'
    });
  } catch (error) {
    console.log(`   ✅ Validation error caught: ${error.response?.status} ${error.response?.statusText}`);
  }

  console.log('\n2. Testing 404 error (non-existent package):');
  try {
    await axios.get(`${BASE_URL}/packages/507f1f77bcf86cd799439011`); // Valid ObjectId but non-existent
  } catch (error) {
    console.log(`   ✅ 404 error caught: ${error.response?.status} ${error.response?.statusText}`);
  }

  console.log('\n3. Testing invalid ObjectId error:');
  try {
    await axios.get(`${BASE_URL}/packages/invalid-id`);
  } catch (error) {
    console.log(`   ✅ Invalid ObjectId error caught: ${error.response?.status} ${error.response?.statusText}`);
  }

  console.log('\n4. Testing successful package creation (for comparison):');
  try {
    const response = await axios.post(`${BASE_URL}/packages`, {
      title: 'Test Package for Winston Logging',
      description: 'This package is created to test Winston error logging',
      version: '1.0.0',
      author: 'Winston Test',
      dependencies: ['express', 'winston'],
      tags: ['test', 'logging', 'winston']
    });
    console.log(`   ✅ Package created successfully: ${response.status} ${response.statusText}`);
    console.log(`   📦 Package ID: ${response.data.data._id}`);
    
    // Test updating the package
    console.log('\n5. Testing package update:');
    const updateResponse = await axios.put(`${BASE_URL}/packages/${response.data.data._id}`, {
      title: 'Updated Test Package for Winston Logging',
      description: 'This package has been updated to test Winston logging',
      version: '1.1.0'
    });
    console.log(`   ✅ Package updated successfully: ${updateResponse.status} ${updateResponse.statusText}`);
    
    // Test deleting the package
    console.log('\n6. Testing package deletion:');
    const deleteResponse = await axios.delete(`${BASE_URL}/packages/${response.data.data._id}`);
    console.log(`   ✅ Package deleted successfully: ${deleteResponse.status} ${deleteResponse.statusText}`);
    
  } catch (error) {
    console.log(`   ❌ Unexpected error: ${error.message}`);
  }

  console.log('\n7. Testing rate limiting (multiple rapid requests):');
  const promises = [];
  for (let i = 0; i < 20; i++) {
    promises.push(
      axios.get(`${BASE_URL}/packages`).catch(error => ({
        status: error.response?.status,
        message: error.response?.statusText
      }))
    );
  }
  
  try {
    const results = await Promise.all(promises);
    const rateLimited = results.filter(r => r.status === 429);
    console.log(`   ✅ Made 20 requests, ${rateLimited.length} were rate limited`);
  } catch (error) {
    console.log(`   ❌ Rate limiting test error: ${error.message}`);
  }

  console.log('\n✅ API error logging tests completed!');
  console.log('📁 Check the ./logs/ directory for detailed Winston logs');
  console.log('🔍 Look for:');
  console.log('   - error-*.log for error-level logs');
  console.log('   - combined-*.log for all logs including HTTP requests');
  console.log('   - access-*.log for HTTP access logs');
}

// Run the tests
testAPIErrors().catch(console.error);
