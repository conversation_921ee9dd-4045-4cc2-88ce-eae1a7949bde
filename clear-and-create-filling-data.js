const mongoose = require('mongoose');
require('dotenv').config();

// Import models (recreate schemas since we can't import TypeScript)
const { Schema } = mongoose;

// Business Schema (simplified)
const BusinessSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  businessName: { type: String, required: true },
  primaryEmail: { type: String, required: true },
  primary: { type: Boolean, default: false },
  country: { type: String, default: "" },
  businessLogo: { type: String, default: "" },
  businessCategory: { type: String, default: "General" },
  businessType: { type: String, required: true },
}, { timestamps: true });

// Transaction Schema
const TransactionSchema = new Schema({
  walletId: { type: Schema.Types.ObjectId, ref: "Wallet", required: true },
  txId: { type: String, required: true, unique: true },
  txRef: { type: String, required: true, unique: true },
  shipmentId: { type: Schema.Types.ObjectId, ref: "Shipment", required: false },
  amount: { type: Number, required: true },
  currency: { type: String, required: true },
  type: { type: String, enum: ["funding", "payment", "withdrawal"], required: true },
  status: { type: String, enum: ["pending", "successful", "failed"], default: "pending" },
  meta: { type: Map, of: Schema.Types.Mixed, required: true },
  paymentGatewayResponse: { type: Schema.Types.Mixed, required: false },
}, { timestamps: true });

// Filling Records Schema
const businessNamesSchema = new Schema({
  type: { type: String, required: true, trim: true },
  name1: { type: String, required: true, trim: true },
  name2: { type: String, required: false, trim: true },
  name3: { type: String, required: false, trim: true },
  phone: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  businessNature: { type: String, required: true, trim: true },
});

const directorSchema = new Schema({
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  otherName: { type: String, required: false, trim: true },
  dateOfBirth: { type: Date, required: true },
  gender: { type: String, enum: ["male", "female", "other"], required: true },
  nationality: { type: String, required: true, trim: true },
  phone: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  occupation: { type: String, required: true, trim: true },
  address: { type: String, required: true, trim: true },
  nin: { type: String, required: true, trim: true },
  identification: { type: String, required: true, trim: true },
  passport: { type: String, required: true, trim: true },
  signature: { type: String, required: true, trim: true },
});

const witnessSchema = new Schema({
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  otherName: { type: String, required: false, trim: true },
  dateOfBirth: { type: Date, required: true },
  gender: { type: String, enum: ["male", "female", "other"], required: true },
  nationality: { type: String, required: true, trim: true },
  phone: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  occupation: { type: String, required: true, trim: true },
  address: { type: String, required: true, trim: true },
  witnessSignature: { type: String, required: false, trim: true },
});

const registrationFormSchema = new Schema({
  businessId: { type: Schema.Types.ObjectId, ref: "Business", required: true },
  details: {
    businessNames: { type: businessNamesSchema, required: true },
    director: { type: directorSchema, required: true },
    witness: { type: witnessSchema, required: true },
  },
  txId: { type: String, required: true, unique: true, index: true, trim: true },
  file: [{
    title: { type: String, required: false, trim: true },
    url: { type: String, required: false }
  }],
  status: { type: String, enum: ["pending", "approved", "rejected"], required: true, default: "pending" },
  document: { type: String, required: true, trim: true },
}, { timestamps: true });

// Create models
const Business = mongoose.model('Business', BusinessSchema);
const Transaction = mongoose.model('Transaction', TransactionSchema);
const RegistrationForm = mongoose.model('RegistrationForm', registrationFormSchema);

// Sample data with Nigerian context and Naira currency
const sampleData = {
  businesses: [
    {
      businessName: "Lagos Tech Solutions Ltd",
      primaryEmail: "<EMAIL>",
      businessType: "Limited Liability Company",
      businessCategory: "Technology",
      country: "Nigeria"
    },
    {
      businessName: "Abuja Trading Company",
      primaryEmail: "<EMAIL>", 
      businessType: "Private Limited Company",
      businessCategory: "Trading",
      country: "Nigeria"
    },
    {
      businessName: "Port Harcourt Manufacturing Ltd",
      primaryEmail: "<EMAIL>",
      businessType: "Limited Liability Company", 
      businessCategory: "Manufacturing",
      country: "Nigeria"
    }
  ],
  
  transactions: [
    {
      amount: 50000, // 50,000 Naira
      currency: "NGN",
      type: "payment",
      status: "successful",
      meta: new Map([
        ["service", "business_registration"],
        ["description", "Business registration fee"],
        ["location", "Lagos, Nigeria"]
      ])
    },
    {
      amount: 75000, // 75,000 Naira
      currency: "NGN", 
      type: "payment",
      status: "successful",
      meta: new Map([
        ["service", "business_registration"],
        ["description", "Premium business registration package"],
        ["location", "Abuja, Nigeria"]
      ])
    },
    {
      amount: 100000, // 100,000 Naira
      currency: "NGN",
      type: "payment", 
      status: "pending",
      meta: new Map([
        ["service", "business_registration"],
        ["description", "Complete business setup package"],
        ["location", "Port Harcourt, Nigeria"]
      ])
    }
  ],

  fillingRecords: [
    {
      details: {
        businessNames: {
          type: "Limited Liability Company",
          name1: "Lagos Tech Solutions Ltd",
          name2: "LTS Ltd",
          phone: "+234-************",
          email: "<EMAIL>",
          businessNature: "Software Development and IT Services"
        },
        director: {
          firstName: "Adebayo",
          lastName: "Ogundimu", 
          otherName: "Olumide",
          dateOfBirth: new Date("1985-03-15"),
          gender: "male",
          nationality: "Nigerian",
          phone: "+234-************",
          email: "<EMAIL>",
          occupation: "Software Engineer",
          address: "15 Victoria Island, Lagos State, Nigeria",
          nin: "***********",
          identification: "lagos_drivers_license_001.jpg",
          passport: "passport_adebayo_001.jpg", 
          signature: "signature_adebayo_001.jpg"
        },
        witness: {
          firstName: "Funmilayo",
          lastName: "Adebisi",
          dateOfBirth: new Date("1988-07-22"),
          gender: "female", 
          nationality: "Nigerian",
          phone: "+234-************",
          email: "<EMAIL>",
          occupation: "Legal Practitioner",
          address: "22 Ikoyi Road, Lagos State, Nigeria",
          witnessSignature: "witness_signature_funmilayo_001.jpg"
        }
      },
      status: "approved",
      document: "registration_certificate_lagostech_001.pdf"
    },
    {
      details: {
        businessNames: {
          type: "Private Limited Company",
          name1: "Abuja Trading Company",
          name2: "ATC Ltd",
          name3: "Abuja Traders",
          phone: "+234-************", 
          email: "<EMAIL>",
          businessNature: "Import and Export Trading"
        },
        director: {
          firstName: "Ibrahim",
          lastName: "Musa",
          dateOfBirth: new Date("1980-11-08"),
          gender: "male",
          nationality: "Nigerian", 
          phone: "+234-************",
          email: "<EMAIL>",
          occupation: "Business Executive",
          address: "45 Garki District, Abuja FCT, Nigeria",
          nin: "***********",
          identification: "abuja_national_id_002.jpg",
          passport: "passport_ibrahim_002.jpg",
          signature: "signature_ibrahim_002.jpg"
        },
        witness: {
          firstName: "Aisha",
          lastName: "Abdullahi",
          dateOfBirth: new Date("1983-04-12"),
          gender: "female",
          nationality: "Nigerian",
          phone: "+234-************", 
          email: "<EMAIL>",
          occupation: "Accountant",
          address: "12 Wuse II, Abuja FCT, Nigeria",
          witnessSignature: "witness_signature_aisha_002.jpg"
        }
      },
      status: "pending",
      document: "registration_application_abujatrading_002.pdf"
    },
    {
      details: {
        businessNames: {
          type: "Limited Liability Company",
          name1: "Port Harcourt Manufacturing Ltd",
          name2: "PHM Ltd",
          phone: "+234-************",
          email: "<EMAIL>", 
          businessNature: "Industrial Manufacturing and Processing"
        },
        director: {
          firstName: "Chinedu",
          lastName: "Okwu",
          otherName: "Emmanuel",
          dateOfBirth: new Date("1978-09-25"),
          gender: "male",
          nationality: "Nigerian",
          phone: "+234-************",
          email: "<EMAIL>",
          occupation: "Industrial Engineer", 
          address: "78 Trans Amadi, Port Harcourt, Rivers State, Nigeria",
          nin: "***********",
          identification: "rivers_voters_card_003.jpg",
          passport: "passport_chinedu_003.jpg",
          signature: "signature_chinedu_003.jpg"
        },
        witness: {
          firstName: "Blessing",
          lastName: "Eze",
          dateOfBirth: new Date("1990-01-30"),
          gender: "female",
          nationality: "Nigerian",
          phone: "+234-************",
          email: "<EMAIL>", 
          occupation: "Project Manager",
          address: "33 GRA Phase II, Port Harcourt, Rivers State, Nigeria",
          witnessSignature: "witness_signature_blessing_003.jpg"
        }
      },
      status: "rejected",
      document: "registration_draft_phmanufacturing_003.pdf"
    }
  ]
};

async function clearAndCreateFillingData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    await RegistrationForm.deleteMany({});
    await Transaction.deleteMany({});
    await Business.deleteMany({});
    console.log('✅ Cleared existing filling records, transactions, and businesses');

    // Create businesses first (we need their IDs for other records)
    console.log('🏢 Creating businesses...');
    const createdBusinesses = [];
    for (let i = 0; i < sampleData.businesses.length; i++) {
      const businessData = {
        ...sampleData.businesses[i],
        userId: new mongoose.Types.ObjectId(), // Generate fake user ID
        primary: i === 0 // Make first business primary
      };
      const business = new Business(businessData);
      const savedBusiness = await business.save();
      createdBusinesses.push(savedBusiness);
      console.log(`   ✅ Created: ${savedBusiness.businessName} (ID: ${savedBusiness._id})`);
    }

    // Create transactions with Naira currency
    console.log('💰 Creating transactions with NGN currency...');
    const createdTransactions = [];
    for (let i = 0; i < sampleData.transactions.length; i++) {
      const transactionData = {
        ...sampleData.transactions[i],
        walletId: new mongoose.Types.ObjectId(), // Generate fake wallet ID
        txId: `NGN_TX_${Date.now()}_${i + 1}`,
        txRef: `REF_NGN_${Date.now()}_${i + 1}`,
      };
      const transaction = new Transaction(transactionData);
      const savedTransaction = await transaction.save();
      createdTransactions.push(savedTransaction);
      console.log(`   ✅ Created: ₦${savedTransaction.amount.toLocaleString()} ${savedTransaction.currency} transaction (ID: ${savedTransaction._id})`);
    }

    // Create filling records
    console.log('📋 Creating filling records...');
    const createdFillingRecords = [];
    for (let i = 0; i < sampleData.fillingRecords.length; i++) {
      const fillingData = {
        ...sampleData.fillingRecords[i],
        businessId: createdBusinesses[i]._id,
        txId: createdTransactions[i].txId,
        file: [
          {
            title: "Certificate of Incorporation",
            url: `https://documents.coconut.ng/certificates/cert_${i + 1}.pdf`
          },
          {
            title: "Memorandum of Association", 
            url: `https://documents.coconut.ng/memorandum/memo_${i + 1}.pdf`
          }
        ]
      };
      const fillingRecord = new RegistrationForm(fillingData);
      const savedRecord = await fillingRecord.save();
      createdFillingRecords.push(savedRecord);
      console.log(`   ✅ Created: ${savedRecord.details.businessNames.name1} filling record (ID: ${savedRecord._id})`);
    }

    console.log('\n🎉 Sample data created successfully with Nigerian businesses and Naira currency!');
    console.log('\n📊 Summary:');
    console.log(`   🏢 Businesses: ${createdBusinesses.length}`);
    console.log(`   💰 Transactions: ${createdTransactions.length} (All in NGN)`);
    console.log(`   📋 Filling Records: ${createdFillingRecords.length}`);
    
    console.log('\n🔍 Business IDs for Postman testing:');
    createdBusinesses.forEach((business, index) => {
      console.log(`   ${index + 1}. ${business.businessName}`);
      console.log(`      Business ID: ${business._id}`);
      console.log(`      Filling Record ID: ${createdFillingRecords[index]._id}`);
      console.log(`      Transaction: ₦${createdTransactions[index].amount.toLocaleString()} ${createdTransactions[index].currency}`);
      console.log('');
    });

    console.log('🧪 Test these endpoints in Postman:');
    console.log('   GET http://localhost:3000/api/v1/filling/');
    console.log(`   GET http://localhost:3000/api/v1/filling/${createdBusinesses[0]._id}`);
    console.log(`   PATCH http://localhost:3000/api/v1/filling/${createdFillingRecords[0]._id}`);

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

clearAndCreateFillingData();
